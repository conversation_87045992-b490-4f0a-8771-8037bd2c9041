[{"id": "hvac-monitoring-tab", "type": "tab", "label": "HVAC 監控通知", "disabled": false, "info": "監控空調設備狀態變化並發送通知", "env": []}, {"id": "climate-state-monitor", "type": "server-state-changed", "z": "hvac-monitoring-tab", "name": "空調設備狀態監控", "server": "home-assistant-server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [{"property": "name", "value": ""}, {"property": "icon", "value": ""}], "entityidfilter": "climate.ke_ting_leng_qi,climate.zhu_wo_leng_qi,climate.ci_wo_leng_qi,fan.kpi_253hwquan_re_jiao_huan_qi_air_speed,humidifier.rdi_640hhchu_shi_ji", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 200, "y": 100, "wires": [["device-name-mapper"]]}, {"id": "device-name-mapper", "type": "function", "z": "hvac-monitoring-tab", "name": "設備名稱映射", "func": "// 設備名稱映射\nconst deviceNames = {\n    'climate.ke_ting_leng_qi': '客廳冷氣',\n    'climate.zhu_wo_leng_qi': '主臥冷氣', \n    'climate.ci_wo_leng_qi': '次臥冷氣',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全熱交換器',\n    'humidifier.rdi_640hhchu_shi_ji': '吊隱除濕機'\n};\n\n// 獲取設備友好名稱\nconst entityId = msg.data.entity_id;\nconst deviceName = deviceNames[entityId] || entityId;\n\n// 獲取狀態變化信息\nconst oldState = msg.data.old_state ? msg.data.old_state.state : '未知';\nconst newState = msg.data.new_state ? msg.data.new_state.state : '未知';\n\n// 生成時間戳\nconst timestamp = new Date().toLocaleString('zh-TW', {\n    timeZone: 'Asia/Taipei',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n});\n\n// 判斷狀態變化類型\nlet statusChange = '';\nlet icon = '';\n\n// 根據設備類型判斷開關狀態\nif (entityId.startsWith('climate.')) {\n    // 冷氣設備\n    if (newState === 'off') {\n        statusChange = '關閉';\n        icon = '❄️🔴';\n    } else if (newState === 'cool' || newState === 'heat' || newState === 'auto') {\n        statusChange = `開啟 (${newState})`;\n        icon = '❄️🟢';\n    } else {\n        statusChange = `狀態變更為 ${newState}`;\n        icon = '❄️🟡';\n    }\n} else if (entityId.startsWith('fan.')) {\n    // 風扇設備 (全熱交換器)\n    if (newState === 'off') {\n        statusChange = '關閉';\n        icon = '🌀🔴';\n    } else {\n        statusChange = `開啟 (風速: ${newState})`;\n        icon = '🌀🟢';\n    }\n} else if (entityId.startsWith('humidifier.')) {\n    // 除濕機\n    if (newState === 'off') {\n        statusChange = '關閉';\n        icon = '💧🔴';\n    } else {\n        statusChange = `開啟 (${newState})`;\n        icon = '💧🟢';\n    }\n}\n\n// 構建通知訊息\nconst message = `${icon} ${deviceName} ${statusChange}\\n時間: ${timestamp}`;\n\n// 設置輸出訊息\nmsg.payload = {\n    deviceName: deviceName,\n    entityId: entityId,\n    oldState: oldState,\n    newState: newState,\n    statusChange: statusChange,\n    timestamp: timestamp,\n    message: message,\n    icon: icon\n};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 450, "y": 100, "wires": [["notification-router"]]}]